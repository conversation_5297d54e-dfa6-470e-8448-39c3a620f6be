// Developer Portal API Types
// Based on the API documentation in api.md

export interface Developer {
  id: string;
  name: string;
  email: string;
  website?: string;
  totalApps?: number;
  totalDownloads?: number;
}

export interface AppListItem {
  id: string;
  name: string;
  description: string;
  version: string;
  category: string;
  downloads: number;
  fileSize: number;
  iconUrl: string;
  screenshots: string[];
  developer: Developer;
  createdAt: string;
  updatedAt: string;
}

export interface SystemRequirements {
  minWindows?: string;
  minMacOS?: string;
  minLinux?: string;
  diskSpace: string;
  ram: string;
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: string[];
}

export interface AppDetails extends AppListItem {
  longDescription: string;
  tags: string[];
  rating: number;
  reviewCount: number;
  fileSizeFormatted: string;
  systemRequirements: SystemRequirements;
  permissions: string[];
  changelog: ChangelogEntry[];
  supportedLanguages: string[];
  website?: string;
  supportEmail?: string;
  privacyPolicy?: string;
  license: string;
  publishedAt: string;
}

export interface Pagination {
  currentPage: number;
  totalPages: number;
  totalApps: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface ListFilters {
  search?: string;
  category?: string;
  sortBy?: 'name' | 'downloads' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface AppListResponse {
  success: boolean;
  apps: AppListItem[];
  pagination: Pagination;
  filters: ListFilters;
}

export interface AppInfoResponse {
  success: boolean;
  app: AppDetails;
}

export interface DownloadInfo {
  directUrl: string;
  streamingUrl: string;
  fileName: string;
  contentType: string;
  supportsRangeRequests: boolean;
}

export interface AppDownloadResponse {
  success: boolean;
  app: {
    id: string;
    name: string;
    version: string;
    fileSize: number;
    developer: Pick<Developer, 'id' | 'name'>;
    lastModified: string;
  };
  download: DownloadInfo;
  downloads: number;
  timestamp: string;
  etag: string;
}

export interface ApiError {
  error: string;
  code?: string;
}

export interface ListAppsParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  sortBy?: 'name' | 'downloads' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// Rate limiting headers
export interface RateLimitHeaders {
  'X-RateLimit-Limit': string;
  'X-RateLimit-Remaining': string;
  'X-RateLimit-Reset': string;
}
