// Developer Portal API Client
// Handles communication with the AveHub Developer Portal API

import {
  AppListResponse,
  AppInfoResponse,
  AppDownloadResponse,
  ListAppsParams,
  ApiError,
} from '@/types/developer-api';

// API Configuration
const DEVELOPER_API_BASE_URL = 'https://developer.avehubs.com';
const API_KEY = 'AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI';

// Custom error class for API errors
export class DeveloperApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'DeveloperApiError';
  }
}

// Helper function to create headers
function createHeaders(): HeadersInit {
  return {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json',
  };
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorData: ApiError;
    try {
      errorData = await response.json();
    } catch {
      errorData = { error: `HTTP ${response.status}: ${response.statusText}` };
    }
    
    throw new DeveloperApiError(
      errorData.error,
      response.status,
      errorData.code
    );
  }

  return response.json();
}

// Helper function to build query string
function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  
  return searchParams.toString();
}

/**
 * List apps with pagination and filtering
 */
export async function listApps(params: ListAppsParams = {}): Promise<AppListResponse> {
  const queryString = buildQueryString(params);
  const url = `${DEVELOPER_API_BASE_URL}/app/list${queryString ? `?${queryString}` : ''}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
    });
    
    return handleResponse<AppListResponse>(response);
  } catch (error) {
    if (error instanceof DeveloperApiError) {
      throw error;
    }
    throw new DeveloperApiError(
      `Failed to fetch apps: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

/**
 * Get detailed information about a specific app
 */
export async function getAppInfo(appId: string): Promise<AppInfoResponse> {
  if (!appId) {
    throw new DeveloperApiError('App ID is required', 400);
  }
  
  const url = `${DEVELOPER_API_BASE_URL}/app/info/${encodeURIComponent(appId)}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
    });
    
    return handleResponse<AppInfoResponse>(response);
  } catch (error) {
    if (error instanceof DeveloperApiError) {
      throw error;
    }
    throw new DeveloperApiError(
      `Failed to fetch app info: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

/**
 * Get download information for an app
 */
export async function getAppDownloadInfo(appId: string): Promise<AppDownloadResponse> {
  if (!appId) {
    throw new DeveloperApiError('App ID is required', 400);
  }
  
  const url = `${DEVELOPER_API_BASE_URL}/app/download/${encodeURIComponent(appId)}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
    });
    
    return handleResponse<AppDownloadResponse>(response);
  } catch (error) {
    if (error instanceof DeveloperApiError) {
      throw error;
    }
    throw new DeveloperApiError(
      `Failed to fetch download info: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

/**
 * Download an app file directly
 * Returns a Response object for streaming downloads
 */
export async function downloadAppFile(appId: string, rangeHeader?: string): Promise<Response> {
  if (!appId) {
    throw new DeveloperApiError('App ID is required', 400);
  }
  
  const url = `${DEVELOPER_API_BASE_URL}/app/download/${encodeURIComponent(appId)}`;
  const headers: HeadersInit = {
    'Authorization': `Bearer ${API_KEY}`,
  };
  
  if (rangeHeader) {
    headers['Range'] = rangeHeader;
  }
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
    });
    
    if (!response.ok) {
      let errorData: ApiError;
      try {
        errorData = await response.json();
      } catch {
        errorData = { error: `HTTP ${response.status}: ${response.statusText}` };
      }
      
      throw new DeveloperApiError(
        errorData.error,
        response.status,
        errorData.code
      );
    }
    
    return response;
  } catch (error) {
    if (error instanceof DeveloperApiError) {
      throw error;
    }
    throw new DeveloperApiError(
      `Failed to download app: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

/**
 * Utility function to format file size
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Utility function to format download count
 */
export function formatDownloadCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
}
