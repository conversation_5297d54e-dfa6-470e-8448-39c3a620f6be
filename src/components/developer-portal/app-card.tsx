"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AppListItem } from "@/types/developer-api";
import { formatFileSize, formatDownloadCount } from "@/lib/developer-api";
import { Download, Calendar, User } from "lucide-react";

interface AppCardProps {
  app: AppListItem;
  onDownload?: (appId: string) => void;
  isDownloading?: boolean;
}

export function AppCard({ app, onDownload, isDownloading = false }: AppCardProps) {
  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDownload?.(app.id);
  };

  return (
    <Link href={`/developer-portal/apps/${app.id}`}>
      <Card className="group hover:border-blue-500 transition-all duration-200 cursor-pointer h-full">
        <CardContent className="p-4">
          {/* App Icon and Basic Info */}
          <div className="flex items-start gap-3 mb-3">
            <div className="relative w-12 h-12 flex-shrink-0">
              <Image
                src={app.iconUrl}
                alt={`${app.name} icon`}
                fill
                className="rounded-lg object-cover"
                sizes="48px"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-white group-hover:text-blue-400 transition-colors truncate">
                {app.name}
              </h3>
              <p className="text-sm text-gray-400 truncate">v{app.version}</p>
              <span className="inline-block px-2 py-1 text-xs bg-blue-600 text-white rounded-full mt-1">
                {app.category}
              </span>
            </div>
          </div>

          {/* Description */}
          <p className="text-sm text-gray-300 line-clamp-2 mb-3">
            {app.description}
          </p>

          {/* Stats */}
          <div className="flex items-center gap-4 text-xs text-gray-400 mb-3">
            <div className="flex items-center gap-1">
              <Download className="w-3 h-3" />
              <span>{formatDownloadCount(app.downloads)}</span>
            </div>
            <div className="flex items-center gap-1">
              <span>📦</span>
              <span>{formatFileSize(app.fileSize)}</span>
            </div>
            <div className="flex items-center gap-1">
              <User className="w-3 h-3" />
              <span className="truncate">{app.developer.name}</span>
            </div>
          </div>

          {/* Screenshots Preview */}
          {app.screenshots.length > 0 && (
            <div className="flex gap-1 mb-3">
              {app.screenshots.slice(0, 3).map((screenshot, index) => (
                <div key={index} className="relative w-16 h-10 flex-shrink-0">
                  <Image
                    src={screenshot}
                    alt={`${app.name} screenshot ${index + 1}`}
                    fill
                    className="rounded object-cover"
                    sizes="64px"
                  />
                </div>
              ))}
              {app.screenshots.length > 3 && (
                <div className="w-16 h-10 bg-gray-700 rounded flex items-center justify-center text-xs text-gray-400">
                  +{app.screenshots.length - 3}
                </div>
              )}
            </div>
          )}

          {/* Updated Date */}
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Calendar className="w-3 h-3" />
            <span>Updated {new Date(app.updatedAt).toLocaleDateString()}</span>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0">
          <Button
            onClick={handleDownload}
            disabled={isDownloading}
            className="w-full"
            size="sm"
          >
            {isDownloading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Downloading...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Download
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </Link>
  );
}
