/*
 * Developer Portal Apps List API Route
 * Proxies requests to the AveHub Developer Portal API
 */

import { NextRequest, NextResponse } from "next/server";
import { listApps, DeveloperApiError } from "@/lib/developer-api";
import { ListAppsParams } from "@/types/developer-api";

export const runtime = 'nodejs';

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Extract query parameters
    const { searchParams } = new URL(req.url);
    
    const params: ListAppsParams = {
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      search: searchParams.get('search') || undefined,
      category: searchParams.get('category') || undefined,
      sortBy: (searchParams.get('sortBy') as ListAppsParams['sortBy']) || undefined,
      sortOrder: (searchParams.get('sortOrder') as ListAppsParams['sortOrder']) || undefined,
    };

    // Validate parameters
    if (params.page && (params.page < 1 || !Number.isInteger(params.page))) {
      return NextResponse.json(
        { error: "Page must be a positive integer" },
        { status: 400 }
      );
    }

    if (params.limit && (params.limit < 1 || params.limit > 50 || !Number.isInteger(params.limit))) {
      return NextResponse.json(
        { error: "Limit must be between 1 and 50" },
        { status: 400 }
      );
    }

    if (params.sortBy && !['name', 'downloads', 'createdAt', 'updatedAt'].includes(params.sortBy)) {
      return NextResponse.json(
        { error: "Invalid sortBy parameter" },
        { status: 400 }
      );
    }

    if (params.sortOrder && !['asc', 'desc'].includes(params.sortOrder)) {
      return NextResponse.json(
        { error: "Invalid sortOrder parameter" },
        { status: 400 }
      );
    }

    // Call the developer API
    const result = await listApps(params);

    // Return successful response
    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error("Error in developer portal apps list API:", error);

    if (error instanceof DeveloperApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.status }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
