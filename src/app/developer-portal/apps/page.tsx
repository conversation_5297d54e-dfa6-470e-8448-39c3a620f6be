"use client";

import React, { useState, useEffect, useCallback } from "react";
import { AppCard } from "@/components/developer-portal/app-card";
import { AppFilters } from "@/components/developer-portal/app-filters";
import { Pagination } from "@/components/developer-portal/pagination";
import { LoadingState } from "@/components/ui/loading-spinner";
import { AppListResponse, ListFilters, AppListItem } from "@/types/developer-api";
import { toast } from "sonner";

export default function DeveloperAppsPage() {
  const [apps, setApps] = useState<AppListItem[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalApps: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [filters, setFilters] = useState<ListFilters>({
    sortBy: 'downloads',
    sortOrder: 'desc'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [downloadingApps, setDownloadingApps] = useState<Set<string>>(new Set());

  // Fetch apps from API
  const fetchApps = useCallback(async (page: number = 1, newFilters: ListFilters = filters) => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(newFilters.search && { search: newFilters.search }),
        ...(newFilters.category && { category: newFilters.category }),
        ...(newFilters.sortBy && { sortBy: newFilters.sortBy }),
        ...(newFilters.sortOrder && { sortOrder: newFilters.sortOrder }),
      });

      const response = await fetch(`/api/developer-portal/apps/list?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch apps');
      }

      const data: AppListResponse = await response.json();
      
      if (data.success) {
        setApps(data.apps);
        setPagination(data.pagination);
      } else {
        throw new Error('API returned unsuccessful response');
      }
    } catch (error) {
      console.error('Error fetching apps:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to load apps');
      setApps([]);
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalApps: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      });
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Initial load
  useEffect(() => {
    fetchApps(1, filters);
  }, []);

  // Handle filter changes
  const handleFiltersChange = (newFilters: ListFilters) => {
    setFilters(newFilters);
    fetchApps(1, newFilters);
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    fetchApps(page, filters);
  };

  // Handle app download
  const handleDownload = async (appId: string) => {
    setDownloadingApps(prev => new Set(prev).add(appId));
    
    try {
      // First get download info
      const infoResponse = await fetch(`/api/developer-portal/apps/${appId}/download`);
      
      if (!infoResponse.ok) {
        const errorData = await infoResponse.json();
        throw new Error(errorData.error || 'Failed to get download info');
      }

      const downloadInfo = await infoResponse.json();
      
      if (!downloadInfo.success) {
        throw new Error('Failed to get download information');
      }

      // Start the download
      const downloadResponse = await fetch(`/api/developer-portal/apps/${appId}/download`, {
        method: 'POST',
      });

      if (!downloadResponse.ok) {
        const errorData = await downloadResponse.json();
        throw new Error(errorData.error || 'Download failed');
      }

      // Create download link
      const blob = await downloadResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = downloadInfo.download.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`${downloadInfo.app.name} downloaded successfully!`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error(error instanceof Error ? error.message : 'Download failed');
    } finally {
      setDownloadingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appId);
        return newSet;
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Developer Portal</h1>
        <p className="text-gray-400">
          Discover and download applications from the AveHub developer community
        </p>
      </div>

      {/* Filters */}
      <div className="mb-8">
        <AppFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          isLoading={isLoading}
        />
      </div>

      {/* Loading State */}
      {isLoading && apps.length === 0 && (
        <LoadingState message="Loading apps..." />
      )}

      {/* Apps Grid */}
      {!isLoading && apps.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-white mb-2">No apps found</h3>
          <p className="text-gray-400">
            Try adjusting your search criteria or filters
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {apps.map((app) => (
              <AppCard
                key={app.id}
                app={app}
                onDownload={handleDownload}
                isDownloading={downloadingApps.has(app.id)}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <Pagination
              pagination={pagination}
              onPageChange={handlePageChange}
              isLoading={isLoading}
            />
          )}
        </>
      )}
    </div>
  );
}
