# AveHub Developer Portal API Documentation

This document provides comprehensive information about the AveHub Developer Portal API endpoints for listing, retrieving, and downloading applications.

## Authentication

All API requests require authentication using an API key. Include the API key in your request headers:

```
Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

or alternatively:

```
X-API-Key: AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
```

## Base URL

```
https://your-domain.com/api
```

## Endpoints

### 1. List Apps

Get a paginated list of approved applications.

**Endpoint:** `GET /app/list`

**Headers:**
```
Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
Content-Type: application/json
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 50)
- `search` (optional): Search term for app name or description
- `category` (optional): Filter by category
- `sortBy` (optional): Sort field (name, downloads, createdAt, updatedAt)
- `sortOrder` (optional): Sort order (asc, desc)

**Example Request:**
```bash
curl -H "Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
     "https://your-domain.com/api/app/list?page=1&limit=20&search=productivity"
```

**Response:**
```json
{
  "success": true,
  "apps": [
    {
      "id": "app-id-123",
      "name": "My Awesome App",
      "description": "A productivity app that helps you manage tasks",
      "version": "1.0.0",
      "category": "Productivity",
      "downloads": 1250,
      "fileSize": 15728640,
      "iconUrl": "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/icon-file-id/view?project=683ad2af001d45cae4da",
      "screenshots": [
        "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/screenshot1-id/view?project=683ad2af001d45cae4da",
        "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/screenshot2-id/view?project=683ad2af001d45cae4da"
      ],
      "developer": {
        "id": "dev-id-456",
        "name": "John Developer",
        "email": "<EMAIL>"
      },
      "createdAt": "2023-12-01T10:30:00Z",
      "updatedAt": "2023-12-15T14:20:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalApps": 95,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "filters": {
    "search": "productivity",
    "category": null,
    "sortBy": "downloads",
    "sortOrder": "desc"
  }
}
```

### 2. Get App Information

Retrieve detailed information about a specific application.

**Endpoint:** `GET /app/info/{appId}`

**Headers:**
```
Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
Content-Type: application/json
```

**Path Parameters:**
- `appId`: The unique identifier of the application

**Example Request:**
```bash
curl -H "Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
     "https://your-domain.com/api/app/info/app-id-123"
```

**Response:**
```json
{
  "success": true,
  "app": {
    "id": "app-id-123",
    "name": "My Awesome App",
    "description": "A comprehensive productivity app that helps you manage tasks, organize projects, and boost your efficiency. Features include task scheduling, team collaboration, and advanced reporting.",
    "longDescription": "Detailed description with features, installation instructions, and usage guidelines...",
    "version": "1.0.0",
    "category": "Productivity",
    "tags": ["productivity", "tasks", "collaboration", "organization"],
    "downloads": 1250,
    "rating": 4.5,
    "reviewCount": 87,
    "fileSize": 15728640,
    "fileSizeFormatted": "15.0 MB",
    "iconUrl": "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/icon-file-id/view?project=683ad2af001d45cae4da",
    "screenshots": [
      "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/screenshot1-id/view?project=683ad2af001d45cae4da",
      "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/screenshot2-id/view?project=683ad2af001d45cae4da",
      "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/screenshot3-id/view?project=683ad2af001d45cae4da"
    ],
    "developer": {
      "id": "dev-id-456",
      "name": "John Developer",
      "email": "<EMAIL>",
      "website": "https://johndeveloper.com",
      "totalApps": 5,
      "totalDownloads": 5250
    },
    "systemRequirements": {
      "minWindows": "Windows 10",
      "minMacOS": "macOS 10.15",
      "minLinux": "Ubuntu 18.04",
      "diskSpace": "50 MB",
      "ram": "4 GB"
    },
    "permissions": [
      "Read/Write file access",
      "Network access",
      "System notifications"
    ],
    "changelog": [
      {
        "version": "1.0.0",
        "date": "2023-12-15",
        "changes": [
          "Initial release",
          "Core task management features",
          "Team collaboration tools"
        ]
      }
    ],
    "supportedLanguages": ["en", "es", "fr", "de"],
    "website": "https://myawesomeapp.com",
    "supportEmail": "<EMAIL>",
    "privacyPolicy": "https://myawesomeapp.com/privacy",
    "license": "MIT",
    "createdAt": "2023-12-01T10:30:00Z",
    "updatedAt": "2023-12-15T14:20:00Z",
    "publishedAt": "2023-12-01T12:00:00Z"
  }
}
```

### 3. Download App (Get Download Info)

Get download information and URLs for an application.

**Endpoint:** `GET /app/download/{appId}`

**Headers:**
```
Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
Content-Type: application/json
```

**Path Parameters:**
- `appId`: The unique identifier of the application

**Example Request:**
```bash
curl -H "Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
     "https://your-domain.com/api/app/download/app-id-123"
```

**Response:**
```json
{
  "success": true,
  "app": {
    "id": "app-id-123",
    "name": "My Awesome App",
    "version": "1.0.0",
    "fileSize": 15728640,
    "developer": {
      "id": "dev-id-456",
      "name": "John Developer"
    },
    "lastModified": "2023-12-15T14:20:00Z"
  },
  "download": {
    "directUrl": "https://api.avehubs.com/v1/storage/buckets/68495a210010968428b5/files/app-file-id/download?project=683ad2af001d45cae4da",
    "streamingUrl": "https://your-domain.com/api/app/download/app-id-123",
    "fileName": "My_Awesome_App_v1.0.0.zip",
    "contentType": "application/zip",
    "supportsRangeRequests": true
  },
  "downloads": 1251,
  "timestamp": "2023-12-20T10:15:30Z",
  "etag": "\"app-id-123-1702646400000\""
}
```

### 4. Direct File Download

Download the application file directly (supports resumable downloads).

**Endpoint:** `POST /app/download/{appId}`

**Headers:**
```
Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI
Range: bytes=0-1023 (optional, for partial downloads)
```

**Path Parameters:**
- `appId`: The unique identifier of the application

**Example Request (Full Download):**
```bash
curl -X POST \
     -H "Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
     -o "my-awesome-app.zip" \
     "https://your-domain.com/api/app/download/app-id-123"
```

**Example Request (Resumable Download):**
```bash
curl -X POST \
     -H "Authorization: Bearer AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI" \
     -H "Range: bytes=1024000-" \
     -o "my-awesome-app-part.zip" \
     "https://your-domain.com/api/app/download/app-id-123"
```

**Response Headers:**
```
Content-Type: application/zip
Content-Disposition: attachment; filename="My_Awesome_App_v1.0.0.zip"
Content-Length: 15728640
Accept-Ranges: bytes
Cache-Control: public, max-age=31536000
ETag: "app-id-123-1702646400000"
Last-Modified: Fri, 15 Dec 2023 14:20:00 GMT
```

**For Range Requests (Status 206):**
```
Content-Range: bytes 1024000-15728639/15728640
Content-Length: 14704640
```

## Error Responses

All endpoints may return the following error responses:

### 401 Unauthorized
```json
{
  "error": "Missing or invalid API key"
}
```

### 403 Forbidden
```json
{
  "error": "App not available for download",
  "code": "APP_NOT_AVAILABLE"
}
```

### 404 Not Found
```json
{
  "error": "App not found",
  "code": "APP_NOT_FOUND"
}
```

### 416 Range Not Satisfiable
```json
{
  "error": "Requested range not satisfiable"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "code": "INTERNAL_ERROR"
}
```

## Rate Limiting

API requests are subject to rate limiting:
- **Rate Limit:** 1000 requests per hour per API key
- **Burst Limit:** 100 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1703073600
```

## Best Practices

### 1. Caching
- Use conditional requests with `If-None-Match` and `If-Modified-Since` headers
- Cache app listings for reasonable periods (5-15 minutes)
- Respect cache headers in responses

### 2. Download Optimization
- Use direct download URLs when possible for better performance
- Implement resumable downloads using range requests
- Handle network interruptions gracefully

### 3. Error Handling
- Always check the `success` field in JSON responses
- Implement exponential backoff for retries
- Handle specific error codes appropriately

### 4. Pagination
- Always check `hasNextPage` before making additional requests
- Use reasonable page sizes (10-50 items)
- Implement client-side pagination controls

## SDKs and Examples

### JavaScript/Node.js Example
```javascript
const API_KEY = 'AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI';
const BASE_URL = 'https://your-domain.com/api';

async function listApps(page = 1, limit = 10) {
  const response = await fetch(`${BASE_URL}/app/list?page=${page}&limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${API_KEY}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
}

async function getAppInfo(appId) {
  const response = await fetch(`${BASE_URL}/app/info/${appId}`, {
    headers: {
      'Authorization': `Bearer ${API_KEY}`,
      'Content-Type': 'application/json'
    }
  });
  
  return await response.json();
}

async function downloadApp(appId, outputPath) {
  const response = await fetch(`${BASE_URL}/app/download/${appId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_KEY}`
    }
  });
  
  if (!response.ok) {
    throw new Error(`Download failed: ${response.status}`);
  }
  
  // Handle file download...
  const buffer = await response.arrayBuffer();
  // Save buffer to file...
}
```

### Python Example
```python
import requests
import json

API_KEY = 'AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI'
BASE_URL = 'https://your-domain.com/api'

class AveHubAPI:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def list_apps(self, page=1, limit=10, search=None):
        params = {'page': page, 'limit': limit}
        if search:
            params['search'] = search
            
        response = requests.get(
            f'{self.base_url}/app/list',
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def get_app_info(self, app_id):
        response = requests.get(
            f'{self.base_url}/app/info/{app_id}',
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def download_app(self, app_id, output_path):
        response = requests.post(
            f'{self.base_url}/app/download/{app_id}',
            headers={'Authorization': f'Bearer {self.api_key}'},
            stream=True
        )
        response.raise_for_status()
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

# Usage
api = AveHubAPI(API_KEY, BASE_URL)
apps = api.list_apps(page=1, limit=20)
app_info = api.get_app_info('app-id-123')
api.download_app('app-id-123', 'downloaded_app.zip')
```

## Support

For API support and questions:
- **Email:** <EMAIL>
- **Documentation:** https://your-domain.com/docs
- **Status Page:** https://status.avehubs.com

## Changelog

### Version 1.0.0 (Current)
- Initial API release
- App listing with pagination and filtering
- Detailed app information retrieval
- Optimized download system with resumable downloads
- Comprehensive error handling and rate limiting
